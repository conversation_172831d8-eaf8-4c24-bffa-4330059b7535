import Config

# Note we also include the path to a cache manifest
# containing the digested version of static files. This
# manifest is generated by the `mix assets.deploy` task,
# which you should run after static files are built and
# before starting your production server.
config :reconciliation, ReconciliationWeb.Endpoint,
  cache_static_manifest: "priv/static/cache_manifest.json"



# Do not print debug messages in production
config :logger, level: :info

# Configure Bamboo mailer for production - DISABLED for safety
# External email sending is disabled to prevent accidental emails
config :reconciliation, Reconciliation.Mailer,
  adapter: Bamboo.LocalAdapter

# Runtime production configuration, including reading
# of environment variables, is done on config/runtime.exs.

