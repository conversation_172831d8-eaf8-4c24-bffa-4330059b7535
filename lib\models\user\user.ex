defmodule Reconciliation.Accounts.User do
  use Ecto.Schema
  import Ecto.Changeset
  import Ecto.Query

  alias Reconciliation.Organizations.Organization
  alias Reconciliation.Accounts.{UserProfile, UserActivityLog, UserRoleAssignment, Role}
  alias Reconciliation.ReconciliationRun

  @statuses ["active", "inactive", "suspended", "pending"]

  schema "users" do
    field :email, :string
    field :password, :string, virtual: true, redact: true
    field :hashed_password, :string, redact: true
    field :current_password, :string, virtual: true, redact: true
    field :confirmed_at, :utc_datetime
    field :status, :string
    field :last_login_at, :utc_datetime
    field :failed_login_attempts, :integer
    field :locked_until, :utc_datetime
    field :password_changed_at, :utc_datetime
    field :must_change_password, :boolean

    belongs_to :organization, Organization
    has_one :profile, UserProfile
    has_many :activity_logs, UserActivityLog
    has_many :role_assignments, UserRoleAssignment
    has_many :roles, through: [:role_assignments, :role]
    has_many :reconciliation_runs, ReconciliationRun

    timestamps(type: :utc_datetime)
  end

  @doc """
  A user changeset for registration.

  It is important to validate the length of both email and password.
  Otherwise databases may truncate the email without warnings, which
  could lead to unpredictable or insecure behaviour. Long passwords may
  also be very expensive to hash for certain algorithms.

  ## Options

    * `:hash_password` - Hashes the password so it can be stored securely
      in the database and ensures the password field is cleared to prevent
      leaks in the logs. If password hashing is not needed and clearing the
      password field is not desired (like when using this changeset for
      validations on a LiveView form), this option can be set to `false`.
      Defaults to `true`.

    * `:validate_email` - Validates the uniqueness of the email, in case
      you don't want to validate the uniqueness of the email (like when
      using this changeset for validations on a LiveView form before
      submitting the form), this option can be set to `false`.
      Defaults to `true`.
  """
  def registration_changeset(user, attrs, opts \\ []) do
    user
    |> cast(attrs, [:email, :password])
    |> validate_email(opts)
    |> validate_password(opts)
  end

  defp validate_email(changeset, opts) do
    changeset
    |> validate_required([:email])
    |> validate_format(:email, ~r/^[^\s]+@[^\s]+$/, message: "must have the @ sign and no spaces")
    |> validate_length(:email, max: 160)
    |> maybe_validate_unique_email(opts)
  end

  defp validate_password(changeset, opts) do
    changeset
    |> validate_required([:password])
    |> validate_length(:password, min: 12, max: 72)
    # Examples of additional password validation:
    # |> validate_format(:password, ~r/[a-z]/, message: "at least one lower case character")
    # |> validate_format(:password, ~r/[A-Z]/, message: "at least one upper case character")
    # |> validate_format(:password, ~r/[!?@#$%^&*_0-9]/, message: "at least one digit or punctuation character")
    |> maybe_hash_password(opts)
  end

  defp maybe_hash_password(changeset, opts) do
    hash_password? = Keyword.get(opts, :hash_password, true)
    password = get_change(changeset, :password)

    if hash_password? && password && changeset.valid? do
      changeset
      # Hashing could be done with `Ecto.Changeset.prepare_changes/2`, but that
      # would keep the database transaction open longer and hurt performance.
      |> put_change(:hashed_password, Pbkdf2.hash_pwd_salt(password))
      |> delete_change(:password)
    else
      changeset
    end
  end

  defp maybe_validate_unique_email(changeset, opts) do
    if Keyword.get(opts, :validate_email, true) do
      changeset
      |> unsafe_validate_unique(:email, Reconciliation.Repo)
      |> unique_constraint(:email)
    else
      changeset
    end
  end

  @doc """
  A user changeset for admin user creation with automatic password generation.

  This changeset is used when administrators create new users. It automatically
  generates a secure password instead of requiring manual input.

  Returns a tuple of {changeset, generated_password} so the password can be used
  for email sending.

  ## Options

    * `:validate_email` - Validates the uniqueness of the email. Defaults to `true`.
    * `:hash_password` - Hashes the password so it can be stored securely. Defaults to `true`.
  """
  def admin_registration_changeset(user, attrs, opts \\ []) do
    # Generate a secure password automatically
    generated_password = Reconciliation.PasswordGenerator.generate_temporary_password()

    # Add the generated password to the attributes
    attrs_with_password = Map.put(attrs, "password", generated_password)

    changeset = user
    |> cast(attrs_with_password, [:email, :password])
    |> validate_email(opts)
    |> validate_password(opts)

    {changeset, generated_password}
  end

  @doc """
  A user changeset for changing the email.

  It requires the email to change otherwise an error is added.
  """
  def email_changeset(user, attrs, opts \\ []) do
    user
    |> cast(attrs, [:email])
    |> validate_email(opts)
    |> case do
      %{changes: %{email: _}} = changeset -> changeset
      %{} = changeset -> add_error(changeset, :email, "did not change")
    end
  end

  @doc """
  A user changeset for changing the password.

  ## Options

    * `:hash_password` - Hashes the password so it can be stored securely
      in the database and ensures the password field is cleared to prevent
      leaks in the logs. If password hashing is not needed and clearing the
      password field is not desired (like when using this changeset for
      validations on a LiveView form), this option can be set to `false`.
      Defaults to `true`.
  """
  def password_changeset(user, attrs, opts \\ []) do
    user
    |> cast(attrs, [:password])
    |> validate_confirmation(:password, message: "does not match password")
    |> validate_password(opts)
  end

  @doc """
  Confirms the account by setting `confirmed_at`.
  """
  def confirm_changeset(user) do
    now = DateTime.utc_now() |> DateTime.truncate(:second)
    change(user, confirmed_at: now)
  end

  @doc """
  Verifies the password.

  If there is no user or the user doesn't have a password, we call
  `Pbkdf2.no_user_verify/0` to avoid timing attacks.
  """
  def valid_password?(%Reconciliation.Accounts.User{hashed_password: hashed_password}, password)
      when is_binary(hashed_password) and byte_size(password) > 0 do
    Pbkdf2.verify_pass(password, hashed_password)
  end

  def valid_password?(_, _) do
    Pbkdf2.no_user_verify()
    false
  end

  @doc """
  Validates the current password otherwise adds an error to the changeset.
  """
  def validate_current_password(changeset, password) do
    changeset = cast(changeset, %{current_password: password}, [:current_password])

    if valid_password?(changeset.data, password) do
      changeset
    else
      add_error(changeset, :current_password, "is not valid")
    end
  end

  @doc """
  Returns all valid user statuses
  """
  def statuses, do: @statuses

  @doc """
  Changeset for updating user status
  """
  def status_changeset(user, attrs) do
    user
    |> cast(attrs, [:status])
    |> validate_inclusion(:status, @statuses)
  end

  @doc """
  Changeset for updating login tracking information
  """
  def login_changeset(user, attrs \\ %{}) do
    user
    |> cast(attrs, [:last_login_at, :failed_login_attempts, :locked_until])
    |> validate_number(:failed_login_attempts, greater_than_or_equal_to: 0)
  end

  @doc """
  Changeset for password change tracking
  """
  def password_change_changeset(user, attrs) do
    user
    |> cast(attrs, [:password_changed_at, :must_change_password])
  end

  @doc """
  Check if user is active
  """
  def active?(user) do
    user.status == "active"
  end

  @doc """
  Check if user is locked
  """
  def locked?(user) do
    user.locked_until && DateTime.compare(user.locked_until, DateTime.utc_now()) == :gt
  end

  @doc """
  Check if user must change password
  """
  def must_change_password?(user) do
    user.must_change_password || false
  end

  @doc """
  Lock user account
  """
  def lock_account(user, duration_minutes \\ 30) do
    locked_until = DateTime.utc_now() |> DateTime.add(duration_minutes * 60, :second)

    user
    |> login_changeset(%{locked_until: locked_until})
  end

  @doc """
  Unlock user account
  """
  def unlock_account(user) do
    user
    |> login_changeset(%{locked_until: nil, failed_login_attempts: 0})
  end

  @doc """
  Increment failed login attempts
  """
  def increment_failed_login(user) do
    attempts = (user.failed_login_attempts || 0) + 1

    user
    |> login_changeset(%{failed_login_attempts: attempts})
  end

  @doc """
  Reset failed login attempts
  """
  def reset_failed_login(user) do
    user
    |> login_changeset(%{failed_login_attempts: 0})
  end

  @doc """
  Update last login timestamp
  """
  def update_last_login(user) do
    user
    |> login_changeset(%{last_login_at: DateTime.utc_now()})
  end

  @doc """
  Query for active users
  """
  def active(query \\ __MODULE__) do
    from user in query, where: user.status == "active"
  end

  @doc """
  Query for users by status
  """
  def by_status(query \\ __MODULE__, status) do
    from user in query, where: user.status == ^status
  end

  @doc """
  Query for users by organization
  """
  def by_organization(query \\ __MODULE__, organization_id) do
    from user in query, where: user.organization_id == ^organization_id
  end

  @doc """
  Query for locked users
  """
  def locked(query \\ __MODULE__) do
    now = DateTime.utc_now()
    from user in query, where: user.locked_until > ^now
  end

  @doc """
  Query users with profile preloaded
  """
  def with_profile(query \\ __MODULE__) do
    from user in query, preload: [:profile]
  end

  @doc """
  Query users with roles preloaded
  """
  def with_roles(query \\ __MODULE__) do
    from user in query, preload: [role_assignments: :role]
  end

  @doc """
  Search users by email or profile name
  """
  def search(query \\ __MODULE__, term) do
    search_term = "%#{term}%"
    from user in query,
      left_join: profile in assoc(user, :profile),
      where: ilike(user.email, ^search_term) or
             ilike(profile.first_name, ^search_term) or
             ilike(profile.last_name, ^search_term)
  end

  @doc """
  Get user's roles
  """
  def get_roles(user) do
    case user.role_assignments do
      %Ecto.Association.NotLoaded{} ->
        # If not preloaded, query the roles
        from(ra in UserRoleAssignment,
          where: ra.user_id == ^user.id,
          join: r in assoc(ra, :role),
          select: r
        )
        |> Reconciliation.Repo.all()

      role_assignments ->
        Enum.map(role_assignments, & &1.role)
    end
  end

  @doc """
  Check if user has role
  """
  def has_role?(user, role_name) do
    user
    |> get_roles()
    |> Enum.any?(&(&1.name == role_name))
  end

  @doc """
  Check if user has permission for resource and action
  """
  def has_permission?(user, resource, action) do
    user
    |> get_roles()
    |> Enum.any?(&Role.has_permission?(&1, resource, action))
  end

  @doc """
  Get user's display name (from profile or email)
  """
  def display_name(user) do
    case user.profile do
      %UserProfile{} = profile ->
        UserProfile.display_name(profile, user)
      _ ->
        user.email
    end
  end
end
