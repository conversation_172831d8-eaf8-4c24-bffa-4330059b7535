defmodule Reconciliation.Services.ExcelExport do
  @moduledoc """
  Service for exporting data to Excel (.xlsx) format with professional styling.
  Supports both template-based exports and programmatic generation.
  """

  alias Elixlsx.{Workbook, Sheet}
  alias UmyaSpreadsheet
  require Logger

  @template_filename "transactions_Reconciliation.xlsx"
  @completed_folder "completed"

  @doc """
  Generate Excel content using the template file approach with preserved formatting.
  Uses umya_spreadsheet_ex to maintain conditional formatting while populating data.
  """
  def generate_transactions_excel_from_template(transactions, run_name \\ "export") do
    try do
      # Try the new approach with preserved formatting first
      case generate_with_preserved_formatting(transactions, run_name) do
        {:ok, path, filename} ->
          # Verify the file is valid before returning
          if File.exists?(path) and File.stat!(path).size > 1000 do
            {:ok, path, filename}
          else
            Logger.warn("Generated file appears invalid, falling back to dual-file approach")
            generate_dual_file_approach(transactions, run_name)
          end
        {:error, reason} ->
          # Fallback to dual-file approach if new method fails
          Logger.warn("Preserved formatting approach failed (#{reason}), falling back to dual-file approach")
          generate_dual_file_approach(transactions, run_name)
      end
    rescue
      error ->
        Logger.error("Failed to generate Excel from template: #{inspect(error)}")
        {:error, Exception.message(error)}
    end
  end

  @doc """
  Generate Excel with preserved conditional formatting using umya_spreadsheet_ex.
  """
  def generate_with_preserved_formatting(transactions, run_name \\ "export") do
    try do
      # Get the project root directory
      project_root = File.cwd!()
      template_path = Path.join(project_root, @template_filename)

      # Ensure completed folder exists
      completed_dir = Path.join(project_root, @completed_folder)
      File.mkdir_p!(completed_dir)

      # Check if template exists
      unless File.exists?(template_path) do
        Logger.error("Template file not found: #{template_path}")
        {:error, "Template file not found: #{@template_filename}"}
      else
        # Generate unique filename for the completed export
        timestamp = DateTime.utc_now() |> DateTime.to_iso8601(:basic) |> String.replace(":", "-")
        completed_filename = "transactions_#{run_name}_#{timestamp}_formatted.xlsx"
        completed_path = Path.join(completed_dir, completed_filename)

        # Use umya_spreadsheet_ex to preserve formatting while populating data
        case populate_template_with_formatting(template_path, transactions, completed_path) do
          {:ok, _} ->
            Logger.info("Successfully created formatted Excel with #{length(transactions)} transactions")
            {:ok, completed_path, completed_filename}

          {:error, reason} ->
            {:error, reason}
        end
      end

    rescue
      error ->
        Logger.error("Failed to generate Excel with preserved formatting: #{inspect(error)}")
        {:error, Exception.message(error)}
    end
  end
  # Fallback dual-file approach (original implementation)
  defp generate_dual_file_approach(transactions, run_name) do
    try do
      # Get the project root directory
      project_root = File.cwd!()
      template_path = Path.join(project_root, @template_filename)

      # Ensure completed folder exists
      completed_dir = Path.join(project_root, @completed_folder)
      File.mkdir_p!(completed_dir)

      # Check if template exists
      unless File.exists?(template_path) do
        Logger.error("Template file not found: #{template_path}")
        {:error, "Template file not found: #{@template_filename}"}
      else
        # Generate unique filename for the completed export
        timestamp = DateTime.utc_now() |> DateTime.to_iso8601(:basic) |> String.replace(":", "-")

        # Create populated data file (with transaction data)
        data_filename = "transactions_#{run_name}_#{timestamp}_populated.xlsx"
        data_path = Path.join(completed_dir, data_filename)

        # Create template copy (with preserved formatting)
        template_filename = "transactions_#{run_name}_#{timestamp}_template.xlsx"
        template_copy_path = Path.join(completed_dir, template_filename)

        # Copy original template to preserve formatting
        File.cp!(template_path, template_copy_path)
        Logger.info("Template copied with formatting preserved: #{template_copy_path}")

        # Create populated data file
        case create_populated_data_file(template_path, transactions, data_path) do
          {:ok, _} ->
            Logger.info("Successfully created populated file with #{length(transactions)} transactions")
            Logger.info("Files created:")
            Logger.info("  - Template (formatted): #{template_filename}")
            Logger.info("  - Data (populated): #{data_filename}")

            # Return the populated data file for download
            {:ok, data_path, data_filename}

          {:error, reason} ->
            # Clean up template copy if data creation failed
            File.rm(template_copy_path)
            {:error, reason}
        end
      end

    rescue
      error ->
        Logger.error("Failed to generate Excel from template: #{inspect(error)}")
        {:error, Exception.message(error)}
    end
  end

  # Populate template with data while preserving conditional formatting using umya_spreadsheet_ex
  defp populate_template_with_formatting(template_path, transactions, output_path) do
    try do
      # Load the template file with umya_spreadsheet_ex (preserves all formatting)
      case UmyaSpreadsheet.read(template_path) do
        {:ok, spreadsheet} ->
          # Get the actual sheet name from the template
          sheet_names = UmyaSpreadsheet.get_sheet_names(spreadsheet)
          sheet_name = List.first(sheet_names) || "Sheet1"

          Logger.info("Using sheet: #{sheet_name}")

          # Populate with transaction data (overwriting existing data without clearing)
          # This should preserve any existing conditional formatting rules
          populate_transaction_data_umya(spreadsheet, sheet_name, transactions)

          # Save the populated file with preserved formatting
          case UmyaSpreadsheet.write(spreadsheet, output_path) do
            :ok ->
              Logger.info("Successfully populated template with preserved formatting: #{output_path}")
              {:ok, output_path}
            {:error, reason} ->
              Logger.error("Failed to write formatted Excel file: #{inspect(reason)}")
              {:error, "Failed to write Excel file: #{inspect(reason)}"}
          end

        {:error, reason} ->
          Logger.error("Failed to read template file: #{inspect(reason)}")
          {:error, "Failed to read template file: #{inspect(reason)}"}
      end

    rescue
      error ->
        Logger.error("Failed to populate template with formatting: #{inspect(error)}")
        {:error, Exception.message(error)}
    end
  end



  # Populate spreadsheet with transaction data
  defp populate_transaction_data_umya(spreadsheet, sheet_name, transactions) do
    transactions
    |> Enum.with_index(2)  # Start from row 2 (row 1 has headers)
    |> Enum.each(fn {transaction, row} ->
      populate_transaction_row_umya(spreadsheet, sheet_name, transaction, row)
    end)
  end

  # Populate a single transaction row using umya_spreadsheet_ex
  defp populate_transaction_row_umya(spreadsheet, sheet_name, transaction, row) do
    try do
      # Column mapping based on template structure - with correct field names
      # Order matches default_transaction_headers(): Transaction Date, Transaction ID, Description, Reference, Amount, etc.
      safe_set_cell_value(spreadsheet, sheet_name, "A#{row}", format_date_for_export(transaction.transaction_date))
      safe_set_cell_value(spreadsheet, sheet_name, "B#{row}", safe_string(transaction.transaction_id))
      safe_set_cell_value(spreadsheet, sheet_name, "C#{row}", safe_string(transaction.description))
      safe_set_cell_value(spreadsheet, sheet_name, "D#{row}", safe_string(transaction.reference))
      safe_set_cell_value(spreadsheet, sheet_name, "E#{row}", safe_number(transaction.amount))
      safe_set_cell_value(spreadsheet, sheet_name, "F#{row}", safe_string(transaction.transaction_type))
      safe_set_cell_value(spreadsheet, sheet_name, "G#{row}", safe_string(transaction.account))
      safe_set_cell_value(spreadsheet, sheet_name, "H#{row}", safe_string(transaction.category))
      safe_set_cell_value(spreadsheet, sheet_name, "I#{row}", safe_string(transaction.currency))
      safe_set_cell_value(spreadsheet, sheet_name, "J#{row}", if(transaction.is_matched, do: "Yes", else: "No"))
      safe_set_cell_value(spreadsheet, sheet_name, "K#{row}", safe_number(transaction.match_confidence))
      safe_set_cell_value(spreadsheet, sheet_name, "L#{row}", safe_string(get_source_file_name(transaction)))
      safe_set_cell_value(spreadsheet, sheet_name, "M#{row}", safe_string(get_file_type(transaction)))
      safe_set_cell_value(spreadsheet, sheet_name, "N#{row}", format_date_for_export(transaction.inserted_at))

      # Apply alternating row background color manually
      apply_alternating_row_color(spreadsheet, sheet_name, row)
    rescue
      error ->
        Logger.error("Failed to populate row #{row}: #{inspect(error)}")
        # Continue with next row rather than failing completely
    end
  end

  # Safely set cell value with error handling
  defp safe_set_cell_value(spreadsheet, sheet_name, cell_address, value) do
    try do
      UmyaSpreadsheet.set_cell_value(spreadsheet, sheet_name, cell_address, value)
    rescue
      error ->
        Logger.warn("Failed to set cell #{cell_address}: #{inspect(error)}")
        # Try with empty string as fallback
        UmyaSpreadsheet.set_cell_value(spreadsheet, sheet_name, cell_address, "")
    end
  end

  # Safely convert to string
  defp safe_string(nil), do: ""
  defp safe_string(value) when is_binary(value), do: value
  defp safe_string(value), do: to_string(value)

  # Safely convert to number
  defp safe_number(nil), do: 0
  defp safe_number(%Decimal{} = decimal), do: Decimal.to_float(decimal)
  defp safe_number(value) when is_number(value), do: value
  defp safe_number(_), do: 0

  # Get source file name from uploaded_file association
  defp get_source_file_name(%{uploaded_file: %{filename: filename}}) when is_binary(filename), do: filename
  defp get_source_file_name(%{uploaded_file: %{original_filename: filename}}) when is_binary(filename), do: filename
  defp get_source_file_name(_), do: ""

  # Get file type from uploaded_file association
  defp get_file_type(%{uploaded_file: %{file_type: file_type}}) when is_binary(file_type), do: file_type
  defp get_file_type(_), do: ""

  # Apply alternating row background color in pairs (2 gray, 2 white, 2 gray, 2 white...)
  defp apply_alternating_row_color(spreadsheet, sheet_name, row) do
    try do
      # Calculate which group of 4 rows we're in, starting from row 2
      # Row 2,3 = gray, Row 4,5 = white, Row 6,7 = gray, Row 8,9 = white, etc.
      adjusted_row = row - 2  # Adjust so data rows start from 0
      group_position = rem(adjusted_row, 4)  # Position within each group of 4

      # Apply gray background to first two rows of each group (positions 0,1)
      if group_position < 2 do
        background_color = "#F2F2F2"  # Light gray

        # Apply background to all columns in this row
        for col <- ["A", "B", "C", "D", "E", "F", "G", "H", "I", "J", "K", "L", "M", "N"] do
          cell_address = "#{col}#{row}"
          safe_set_background_color(spreadsheet, sheet_name, cell_address, background_color)
        end
      end
      # Positions 2,3 will remain white (default)
    rescue
      error ->
        Logger.warn("Could not apply alternating row color to row #{row}: #{inspect(error)}")
        # Continue without coloring rather than failing
    end
  end

  # Safely set background color with error handling
  defp safe_set_background_color(spreadsheet, sheet_name, cell_address, color) do
    try do
      UmyaSpreadsheet.set_background_color(spreadsheet, sheet_name, cell_address, color)
    rescue
      error ->
        Logger.warn("Failed to set background color for #{cell_address}: #{inspect(error)}")
        # Continue without background color rather than failing
    end
  end

  # Extend conditional formatting to cover all populated data
  defp extend_conditional_formatting(spreadsheet, sheet_name, transaction_count) do
    try do
      # Calculate the range that needs conditional formatting
      last_row = transaction_count + 1  # +1 because row 1 has headers
      range = "A2:N#{last_row}"  # Cover all data columns

      # Try to add alternating row conditional formatting
      # This creates a simple alternating row pattern
      UmyaSpreadsheet.add_cell_value_rule(
        spreadsheet,
        sheet_name,
        range,
        "formula",
        "MOD(ROW(),2)=0",  # Even rows
        nil,
        %{
          "background_color" => "#F2F2F2",  # Light gray
          "pattern_type" => "solid"
        }
      )

      Logger.info("Extended conditional formatting to range: #{range}")
    rescue
      error ->
        Logger.warn("Could not extend conditional formatting: #{inspect(error)}")
        # Continue without conditional formatting rather than failing
    end
  end

  # Convert column number to Excel letter (1 -> A, 2 -> B, etc.)
  defp column_number_to_letter(num) when num <= 26 do
    <<(?A + num - 1)>>
  end
  defp column_number_to_letter(num) do
    # For columns beyond Z, we'd need more complex logic
    # For now, handle up to column Z (26)
    if num <= 26, do: <<(?A + num - 1)>>, else: "A"
  end

  # Create a populated data file with transaction data (fallback method)
  defp create_populated_data_file(template_path, transactions, output_path) do
    try do
      # Read the template to understand its structure
      case Xlsxir.multi_extract(template_path) do
        [ok: table_id] ->
          existing_rows = Xlsxir.get_list(table_id)
          Xlsxir.close(table_id)

          Logger.info("Template has #{length(existing_rows)} existing rows")

          # Get headers from first row or use defaults
          headers = if length(existing_rows) > 0 do
            Enum.at(existing_rows, 0) || default_transaction_headers()
          else
            default_transaction_headers()
          end

          # Convert transactions to rows
          transaction_rows = Enum.map(transactions, fn transaction ->
            convert_transaction_to_row(transaction, length(headers))
          end)

          # Combine headers with transaction data
          all_rows = [headers | transaction_rows]

          # Create new workbook with the populated data
          # Note: This approach loses conditional formatting but provides populated data
          # For preserving conditional formatting, manual copy/paste is still needed
          workbook = %Workbook{
            sheets: [
              %Sheet{
                name: "Transactions",
                rows: all_rows,
                col_widths: generate_column_widths(length(headers))
              }
            ]
          }

          # Write the populated workbook
          excel_result = Elixlsx.write_to_memory(workbook, "populated_template.xlsx")

          binary_data = case excel_result do
            {:ok, {_filename, data}} -> data
            {:ok, data} -> data
            data when is_binary(data) -> data
            _ -> excel_result
          end

          File.write!(output_path, binary_data)
          Logger.info("Populated template saved to: #{output_path}")

          {:ok, output_path}

        {:error, reason} ->
          Logger.error("Failed to read template: #{inspect(reason)}")
          {:error, "Failed to read template: #{inspect(reason)}"}
      end

    rescue
      error ->
        Logger.error("Failed to populate template directly: #{inspect(error)}")
        {:error, Exception.message(error)}
    end
  end

  # Convert transaction to CSV row
  defp convert_transaction_to_csv_row(transaction) do
    [
      format_date_for_export(transaction.transaction_date),
      transaction.transaction_id || "",
      transaction.description || "",
      transaction.reference || "",
      format_amount_with_commas(transaction.amount),
      transaction.transaction_type || "",
      transaction.account || "",
      transaction.category || "",
      transaction.currency || "USD",
      if(transaction.is_matched, do: "Yes", else: "No"),
      if(transaction.match_confidence, do: format_amount_with_commas(transaction.match_confidence), else: ""),
      if(transaction.uploaded_file, do: clean_filename(transaction.uploaded_file.filename), else: ""),
      if(transaction.uploaded_file, do: transaction.uploaded_file.file_type, else: ""),
      format_date_for_export(transaction.inserted_at)
    ]
  end

  # Format CSV row with proper escaping
  defp format_csv_row(row) do
    row
    |> Enum.map(&escape_csv_field/1)
    |> Enum.join(",")
  end

  # Escape CSV fields
  defp escape_csv_field(field) when is_binary(field) do
    if String.contains?(field, [",", "\"", "\n", "\r"]) do
      "\"#{String.replace(field, "\"", "\"\"")}\""
    else
      field
    end
  end
  defp escape_csv_field(field), do: to_string(field)

  @doc """
  Generate Excel content for reconciliation report with professional styling.
  """
  def generate_reconciliation_report_excel(run, transactions, matches) do
    try do
      # Create headers
      headers = [
        "Transaction Date",
        "Transaction ID",
        "Description",
        "Reference",
        "Amount",
        "Currency",
        "File Source",
        "Match Status",
        "Match Confidence",
        "Match Type",
        "Matched Transaction ID",
        "Verified"
      ]

      # Convert transactions to rows with match information
      rows = transactions
      |> Enum.map(fn transaction ->
        # Find if this transaction has a match
        match_info = find_transaction_match(transaction, matches)

        [
          format_date_for_export(transaction.transaction_date),
          transaction.transaction_id || "",
          transaction.description || "",
          transaction.reference || "",
          format_amount_with_commas(transaction.amount),
          transaction.currency || "USD",
          get_file_source(transaction, run),
          if(transaction.is_matched, do: "Matched", else: "Unmatched"),
          if(match_info, do: format_amount_with_commas(match_info.confidence_score), else: ""),
          if(match_info, do: match_info.match_type, else: ""),
          if(match_info, do: get_matched_transaction_id(transaction, match_info), else: ""),
          if(match_info && match_info.verified_by_user, do: "Yes", else: "No")
        ]
      end)

      # Create workbook with enhanced data
      workbook = %Workbook{
        sheets: [
          %Sheet{
            name: "Reconciliation Report",
            rows: [headers | rows],
            col_widths: %{
              0 => 15,  # Transaction Date
              1 => 20,  # Transaction ID
              2 => 35,  # Description (wider for visual indicators)
              3 => 20,  # Reference
              4 => 15,  # Amount
              5 => 10,  # Currency
              6 => 15,  # File Source
              7 => 15,  # Match Status
              8 => 15,  # Match Confidence
              9 => 15,  # Match Type
              10 => 20, # Matched Transaction ID
              11 => 10  # Verified
            }
          }
        ]
      }

      # Generate Excel binary data and encode as base64 for JavaScript
      excel_result = Elixlsx.write_to_memory(workbook, "reconciliation_report.xlsx")

      # Handle the return format from Elixlsx.write_to_memory
      binary_data = case excel_result do
        {:ok, {_filename, data}} -> data
        {:ok, data} -> data
        data when is_binary(data) -> data
        _ -> excel_result
      end

      base64_data = Base.encode64(binary_data)

      {:ok, base64_data}
    rescue
      error ->
        Logger.error("Failed to generate Excel report: #{inspect(error)}")
        {:error, Exception.message(error)}
    end
  end

  @doc """
  Generate Excel content for transactions export with professional styling.
  """
  def generate_transactions_excel(transactions) do
    try do
      # Create headers
      headers = [
        "Transaction Date",
        "Transaction ID",
        "Description",
        "Reference",
        "Amount",
        "Transaction Type",
        "Account",
        "Category",
        "Currency",
        "Matched",
        "Match Confidence",
        "Source File",
        "File Type",
        "Import Date"
      ]

      # Convert transactions to rows
      rows = transactions
      |> Enum.map(fn transaction ->
        [
          format_date_for_export(transaction.transaction_date),
          transaction.transaction_id || "",
          transaction.description || "",
          transaction.reference || "",
          format_amount_with_commas(transaction.amount),
          transaction.transaction_type || "",
          transaction.account || "",
          transaction.category || "",
          transaction.currency || "USD",
          if(transaction.is_matched, do: "Yes", else: "No"),
          if(transaction.match_confidence, do: format_amount_with_commas(transaction.match_confidence), else: ""),
          if(transaction.uploaded_file, do: clean_filename(transaction.uploaded_file.filename), else: ""),
          if(transaction.uploaded_file, do: transaction.uploaded_file.file_type, else: ""),
          format_date_for_export(transaction.inserted_at)
        ]
      end)

      # Create workbook with enhanced data
      workbook = %Workbook{
        sheets: [
          %Sheet{
            name: "Transactions",
            rows: [headers | rows],
            col_widths: %{
              0 => 15,  # Transaction Date
              1 => 20,  # Transaction ID
              2 => 40,  # Description (wider for visual indicators)
              3 => 20,  # Reference
              4 => 15,  # Amount
              5 => 15,  # Transaction Type
              6 => 15,  # Account
              7 => 15,  # Category
              8 => 10,  # Currency
              9 => 12,  # Matched
              10 => 15, # Match Confidence
              11 => 30, # Source File
              12 => 12, # File Type
              13 => 15  # Import Date
            }
          }
        ]
      }

      # Generate Excel binary data and encode as base64 for JavaScript
      excel_result = Elixlsx.write_to_memory(workbook, "transactions.xlsx")

      # Handle the return format from Elixlsx.write_to_memory
      binary_data = case excel_result do
        {:ok, {_filename, data}} -> data
        {:ok, data} -> data
        data when is_binary(data) -> data
        _ -> excel_result
      end

      base64_data = Base.encode64(binary_data)

      {:ok, base64_data}
    rescue
      error ->
        Logger.error("Failed to generate Excel transactions: #{inspect(error)}")
        {:error, Exception.message(error)}
    end
  end

  # Helper functions (copied from existing export logic)
  
  defp find_transaction_match(transaction, matches) do
    Enum.find(matches, fn match ->
      match.transaction_a_id == transaction.id || match.transaction_b_id == transaction.id
    end)
  end

  defp get_file_source(transaction, run) do
    cond do
      transaction.uploaded_file && transaction.uploaded_file.file_type == "file_a" ->
        run.file_a_name || "File A"
      transaction.uploaded_file && transaction.uploaded_file.file_type == "file_b" ->
        run.file_b_name || "File B"
      true ->
        "Unknown"
    end
  end

  defp get_matched_transaction_id(transaction, match_info) do
    if match_info.transaction_a_id == transaction.id do
      match_info.transaction_b_id
    else
      match_info.transaction_a_id
    end
    |> to_string()
  end

  defp format_date_for_export(nil), do: ""
  defp format_date_for_export(%Date{} = date), do: Date.to_string(date)
  defp format_date_for_export(%DateTime{} = datetime), do: DateTime.to_date(datetime) |> Date.to_string()
  defp format_date_for_export(%NaiveDateTime{} = naive_datetime), do: NaiveDateTime.to_date(naive_datetime) |> Date.to_string()

  # Format amount with commas for better readability
  defp format_amount_with_commas(nil), do: ""
  defp format_amount_with_commas(%Decimal{} = amount) do
    amount
    |> Decimal.to_string(:normal)
    |> add_commas_to_number()
  end
  defp format_amount_with_commas(amount) when is_binary(amount) do
    case Decimal.new(amount) do
      {:ok, decimal} -> format_amount_with_commas(decimal)
      _ -> amount
    end
  end
  defp format_amount_with_commas(amount), do: to_string(amount)

  # Add commas to number string for thousands separator
  defp add_commas_to_number(number_string) do
    # Split on decimal point if present
    case String.split(number_string, ".") do
      [integer_part] ->
        add_commas_to_integer(integer_part)
      [integer_part, decimal_part] ->
        "#{add_commas_to_integer(integer_part)}.#{decimal_part}"
    end
  end

  # Add commas to integer part
  defp add_commas_to_integer(integer_string) do
    integer_string
    |> String.reverse()
    |> String.replace(~r/(\d{3})(?=\d)/, "\\1,")
    |> String.reverse()
  end

  # Clean filename - remove underscores and file extensions for display
  defp clean_filename(nil), do: ""
  defp clean_filename(filename) do
    filename
    |> Path.rootname()  # Remove file extension
    |> String.replace("_", " ")  # Replace underscores with spaces
    |> String.trim()
  end

  # Create a populated workbook based on template structure and transaction data.
  defp create_populated_workbook(template_rows, transactions) do
    try do
      # Analyze template structure
      headers = if length(template_rows) > 0 do
        # Use first row as headers, or create default headers
        first_row = Enum.at(template_rows, 0)
        if first_row && length(first_row) > 0 do
          first_row
        else
          default_transaction_headers()
        end
      else
        default_transaction_headers()
      end

      Logger.info("Using headers: #{inspect(headers)}")

      # Convert transactions to rows matching template structure
      transaction_rows = Enum.map(transactions, fn transaction ->
        convert_transaction_to_row(transaction, length(headers))
      end)

      # Create workbook with template-based structure
      workbook = %Workbook{
        sheets: [
          %Sheet{
            name: "Transactions",
            rows: [headers | transaction_rows],
            col_widths: generate_column_widths(length(headers))
          }
        ]
      }

      {:ok, workbook}
    rescue
      error ->
        Logger.error("Failed to create populated workbook: #{inspect(error)}")
        {:error, Exception.message(error)}
    end
  end

  # Default headers for transaction export
  defp default_transaction_headers do
    [
      "Transaction Date",
      "Transaction ID",
      "Description",
      "Reference",
      "Amount",
      "Transaction Type",
      "Account",
      "Category",
      "Currency",
      "Matched",
      "Match Confidence",
      "Source File",
      "File Type",
      "Import Date"
    ]
  end

  # Convert transaction to row format
  defp convert_transaction_to_row(transaction, column_count) do
    base_row = [
      format_date_for_export(transaction.transaction_date),
      transaction.transaction_id || "",
      transaction.description || "",
      transaction.reference || "",
      format_amount_with_commas(transaction.amount),
      transaction.transaction_type || "",
      transaction.account || "",
      transaction.category || "",
      transaction.currency || "USD",
      if(transaction.is_matched, do: "Yes", else: "No"),
      if(transaction.match_confidence, do: format_amount_with_commas(transaction.match_confidence), else: ""),
      if(transaction.uploaded_file, do: clean_filename(transaction.uploaded_file.filename), else: ""),
      if(transaction.uploaded_file, do: transaction.uploaded_file.file_type, else: ""),
      format_date_for_export(transaction.inserted_at)
    ]

    # Pad or trim to match template column count
    case column_count - length(base_row) do
      0 -> base_row
      diff when diff > 0 -> base_row ++ List.duplicate("", diff)
      diff when diff < 0 -> Enum.take(base_row, column_count)
    end
  end

  # Generate column widths based on number of columns
  defp generate_column_widths(column_count) do
    base_widths = %{
      0 => 15,  # Transaction Date
      1 => 20,  # Transaction ID
      2 => 40,  # Description
      3 => 20,  # Reference
      4 => 15,  # Amount
      5 => 15,  # Transaction Type
      6 => 15,  # Account
      7 => 15,  # Category
      8 => 10,  # Currency
      9 => 12,  # Matched
      10 => 15, # Match Confidence
      11 => 30, # Source File
      12 => 12, # File Type
      13 => 15  # Import Date
    }

    # Add default width for any additional columns
    additional_widths = for i <- 14..(column_count - 1), into: %{}, do: {i, 15}
    Map.merge(base_widths, additional_widths)
  end

  # Generate Excel data as binary (for template population)
  defp generate_transactions_excel_data(transactions) do
    try do
      # Create headers
      headers = [
        "Transaction Date",
        "Transaction ID",
        "Description",
        "Reference",
        "Amount",
        "Transaction Type",
        "Account",
        "Category",
        "Currency",
        "Matched",
        "Match Confidence",
        "Source File",
        "File Type",
        "Import Date"
      ]

      # Convert transactions to rows
      rows = transactions
      |> Enum.map(fn transaction ->
        [
          format_date_for_export(transaction.transaction_date),
          transaction.transaction_id || "",
          transaction.description || "",
          transaction.reference || "",
          format_amount_with_commas(transaction.amount),
          transaction.transaction_type || "",
          transaction.account || "",
          transaction.category || "",
          transaction.currency || "USD",
          if(transaction.is_matched, do: "Yes", else: "No"),
          if(transaction.match_confidence, do: format_amount_with_commas(transaction.match_confidence), else: ""),
          if(transaction.uploaded_file, do: clean_filename(transaction.uploaded_file.filename), else: ""),
          if(transaction.uploaded_file, do: transaction.uploaded_file.file_type, else: ""),
          format_date_for_export(transaction.inserted_at)
        ]
      end)

      # Create workbook with enhanced data
      workbook = %Workbook{
        sheets: [
          %Sheet{
            name: "Transactions",
            rows: [headers | rows],
            col_widths: %{
              0 => 15,  # Transaction Date
              1 => 20,  # Transaction ID
              2 => 40,  # Description (wider for visual indicators)
              3 => 20,  # Reference
              4 => 15,  # Amount
              5 => 15,  # Transaction Type
              6 => 15,  # Account
              7 => 15,  # Category
              8 => 10,  # Currency
              9 => 12,  # Matched
              10 => 15, # Match Confidence
              11 => 30, # Source File
              12 => 12, # File Type
              13 => 15  # Import Date
            }
          }
        ]
      }

      # Generate Excel binary data
      excel_result = Elixlsx.write_to_memory(workbook, "transactions.xlsx")

      # Handle the return format from Elixlsx.write_to_memory
      binary_data = case excel_result do
        {:ok, {_filename, data}} -> data
        {:ok, data} -> data
        data when is_binary(data) -> data
        _ -> excel_result
      end

      {:ok, binary_data}
    rescue
      error ->
        Logger.error("Failed to generate Excel data: #{inspect(error)}")
        {:error, Exception.message(error)}
    end
  end
end
