defmodule Reconciliation.UserManagement do
  @moduledoc """
  The UserManagement context.
  Provides functions for managing users, roles, permissions, and organizations.
  """

  import Ecto.Query, warn: false
  alias Reconciliation.Repo

  alias Reconciliation.Accounts.{
    User, UserProfile, Role, Permission, UserRoleAssignment, UserTeamMembership
  }
  alias Reconciliation.Organizations.{Organization, Team}
  alias Reconciliation.Services.ActivityLogger

  ## User Management

  @doc """
  Returns the list of users with optional filters.
  """
  def list_users(opts \\ []) do
    User
    |> maybe_filter_by_organization(opts[:organization_id])
    |> maybe_filter_by_status(opts[:status])
    |> maybe_search_users(opts[:search])
    |> User.with_profile()
    |> User.with_roles()
    |> Repo.all()
  end

  @doc """
  Gets a single user with profile and roles.
  """
  def get_user!(id) do
    User
    |> User.with_profile()
    |> User.with_roles()
    |> Repo.get!(id)
  end

  @doc """
  Returns an `%Ecto.Changeset{}` for tracking user changes.
  This is used for admin user creation forms and automatically generates passwords.
  """
  def change_user(attrs \\ %{}) do
    # Create a changeset that includes both user and profile fields
    # Use admin_registration_changeset for automatic password generation
    user_attrs = Map.take(attrs, ["email", "organization_id", "status"])

    {changeset, _generated_password} = %User{}
    |> User.admin_registration_changeset(user_attrs)

    changeset
    |> Ecto.Changeset.cast(attrs, [:organization_id, :status])
  end

  @doc """
  Returns an `%Ecto.Changeset{}` for tracking user changes during edit.
  """
  def change_user_for_edit(user) do
    # Create a changeset for editing existing user
    profile_attrs = if user.profile do
      %{
        "first_name" => user.profile.first_name,
        "last_name" => user.profile.last_name,
        "job_title" => user.profile.job_title,
        "department" => user.profile.department,
        "phone_number" => user.profile.phone_number
      }
    else
      %{}
    end

    user_attrs = %{
      "email" => user.email,
      "organization_id" => user.organization_id,
      "status" => user.status
    }

    attrs = Map.merge(profile_attrs, user_attrs)

    user
    |> Ecto.Changeset.change()
    |> Ecto.Changeset.cast(attrs, [:email, :organization_id, :status])
    |> Ecto.Changeset.validate_required([:email])
    |> Ecto.Changeset.validate_format(:email, ~r/^[^\s]+@[^\s]+$/)
    |> Ecto.Changeset.validate_length(:email, max: 160)
  end

  @doc """
  Updates a user and profile (admin function).
  """
  def update_user_admin(user, attrs, updated_by_id \\ nil) do
    Repo.transaction(fn ->
      # Update user
      user_attrs = Map.take(attrs, ["email", "organization_id", "status"])
      user_changeset =
        user
        |> Ecto.Changeset.cast(user_attrs, [:email, :organization_id, :status])
        |> Ecto.Changeset.validate_required([:email])
        |> Ecto.Changeset.validate_format(:email, ~r/^[^\s]+@[^\s]+$/)
        |> Ecto.Changeset.validate_length(:email, max: 160)
        |> Ecto.Changeset.unsafe_validate_unique(:email, Repo)

      case Repo.update(user_changeset) do
        {:ok, updated_user} ->
          # Update or create profile
          profile_attrs = Map.take(attrs, ["first_name", "last_name", "job_title", "department", "phone_number"])

          if updated_user.profile do
            # Update existing profile
            profile_changeset = UserProfile.changeset(updated_user.profile, profile_attrs)
            case Repo.update(profile_changeset) do
              {:ok, _profile} ->
                # Log the activity
                if updated_by_id do
                  ActivityLogger.log_system_activity(updated_by_id, "user_update",
                    resource_type: "user",
                    resource_id: updated_user.id,
                    metadata: %{email: updated_user.email}
                  )
                end
                updated_user
              {:error, changeset} -> Repo.rollback(changeset)
            end
          else
            # Create new profile
            case create_user_profile(updated_user, profile_attrs) do
              {:ok, _profile} ->
                if updated_by_id do
                  ActivityLogger.log_system_activity(updated_by_id, "user_update",
                    resource_type: "user",
                    resource_id: updated_user.id,
                    metadata: %{email: updated_user.email}
                  )
                end
                updated_user
              {:error, changeset} -> Repo.rollback(changeset)
            end
          end
        {:error, changeset} -> Repo.rollback(changeset)
      end
    end)
  end

  @doc """
  Creates a user with profile and sends welcome email with generated password.
  """
  def create_user(attrs \\ %{}, created_by_id \\ nil) do
    Repo.transaction(fn ->
      with {:ok, user, generated_password} <- create_user_record_with_password(attrs),
           {:ok, _profile} <- create_user_profile(user, attrs),
           {:ok, _assignment} <- assign_default_role(user, created_by_id) do

        # Log the user creation
        ActivityLogger.log_system_activity(created_by_id, "user_create",
          resource_type: "user",
          resource_id: user.id,
          metadata: %{email: user.email}
        )

        # Send welcome email with temporary password
        login_url = "#{ReconciliationWeb.Endpoint.url()}/users/log_in"

        case send_welcome_email(user, generated_password, login_url) do
          {:ok, _} ->
            # Log successful email sending
            ActivityLogger.log_system_activity(created_by_id, "user_welcome_email_sent",
              resource_type: "user",
              resource_id: user.id,
              metadata: %{email: user.email, note: "Welcome email sent with temporary password"}
            )

          {:error, reason} ->
            # Log email failure but don't fail the user creation
            ActivityLogger.log_system_activity(created_by_id, "user_welcome_email_failed",
              resource_type: "user",
              resource_id: user.id,
              metadata: %{email: user.email, error: inspect(reason), note: "User created but welcome email failed"}
            )
        end

        user
      else
        {:error, changeset} -> Repo.rollback(changeset)
      end
    end)
  end

  @doc """
  Updates a user.
  """
  def update_user(%User{} = user, attrs, updated_by_id \\ nil) do
    result = user
    |> User.registration_changeset(attrs)
    |> Repo.update()

    case result do
      {:ok, updated_user} ->
        ActivityLogger.log_system_activity(updated_by_id, "user_update",
          resource_type: "user",
          resource_id: user.id,
          metadata: %{changes: Map.keys(attrs)}
        )
        {:ok, updated_user}
      error -> error
    end
  end

  @doc """
  Updates user status.
  """
  def update_user_status(%User{} = user, status, updated_by_id \\ nil) do
    result = user
    |> User.status_changeset(%{status: status})
    |> Repo.update()

    case result do
      {:ok, updated_user} ->
        ActivityLogger.log_system_activity(updated_by_id, "user_update",
          resource_type: "user",
          resource_id: user.id,
          metadata: %{status_change: %{from: user.status, to: status}}
        )
        {:ok, updated_user}
      error -> error
    end
  end

  @doc """
  Deletes a user.
  """
  def delete_user(%User{} = user, deleted_by_id \\ nil) do
    result = Repo.delete(user)

    case result do
      {:ok, deleted_user} ->
        ActivityLogger.log_system_activity(deleted_by_id, "user_delete",
          resource_type: "user",
          resource_id: user.id,
          metadata: %{email: user.email}
        )
        {:ok, deleted_user}
      error -> error
    end
  end

  ## Role Management

  @doc """
  Returns the list of roles.
  """
  def list_roles do
    Repo.all(Role)
  end

  @doc """
  Gets a single role.
  """
  def get_role!(id), do: Repo.get!(Role, id)

  @doc """
  Gets a role by name.
  """
  def get_role_by_name(name) do
    Role
    |> Role.by_name(name)
    |> Repo.one()
  end

  @doc """
  Creates a role.
  """
  def create_role(attrs \\ %{}, created_by_id \\ nil) do
    result = %Role{}
    |> Role.changeset(attrs)
    |> Repo.insert()

    case result do
      {:ok, role} ->
        ActivityLogger.log_system_activity(created_by_id, "role_create",
          resource_type: "role",
          resource_id: role.id,
          metadata: %{name: role.name}
        )
        {:ok, role}
      error -> error
    end
  end

  @doc """
  Assigns a role to a user.
  """
  def assign_role_to_user(user_id, role_id, assigned_by_id, opts \\ []) do
    attrs = UserRoleAssignment.create_assignment(user_id, role_id, assigned_by_id, opts)
    
    result = %UserRoleAssignment{}
    |> UserRoleAssignment.changeset(attrs)
    |> Repo.insert()

    case result do
      {:ok, assignment} ->
        ActivityLogger.log_system_activity(assigned_by_id, "role_assignment",
          resource_type: "user_role_assignment",
          resource_id: assignment.id,
          metadata: %{user_id: user_id, role_id: role_id}
        )
        {:ok, assignment}
      error -> error
    end
  end

  @doc """
  Removes a role from a user.
  """
  def remove_role_from_user(user_id, role_id, removed_by_id \\ nil) do
    assignment = UserRoleAssignment
    |> UserRoleAssignment.by_user(user_id)
    |> UserRoleAssignment.by_role(role_id)
    |> Repo.one()

    if assignment do
      result = Repo.delete(assignment)
      
      case result do
        {:ok, deleted_assignment} ->
          ActivityLogger.log_system_activity(removed_by_id, "role_removal",
            resource_type: "user_role_assignment",
            resource_id: assignment.id,
            metadata: %{user_id: user_id, role_id: role_id}
          )
          {:ok, deleted_assignment}
        error -> error
      end
    else
      {:error, :not_found}
    end
  end

  ## Organization Management

  @doc """
  Returns the list of organizations.
  """
  def list_organizations(opts \\ []) do
    query = Organization

    query = if opts[:active_only] do
      Organization.active(query)
    else
      query
    end

    query
    |> order_by([o], desc: o.inserted_at)
    |> Repo.all()
  end

  @doc """
  Gets a single organization.
  """
  def get_organization(id) do
    Repo.get(Organization, id)
  end

  @doc """
  Gets a single organization.
  """
  def get_organization!(id), do: Repo.get!(Organization, id)

  @doc """
  Creates an organization.
  """
  def create_organization(attrs \\ %{}, created_by_id \\ nil) do
    result = %Organization{}
    |> Organization.changeset(attrs)
    |> Repo.insert()

    case result do
      {:ok, organization} ->
        ActivityLogger.log_system_activity(created_by_id, "organization_create",
          resource_type: "organization",
          resource_id: organization.id,
          metadata: %{name: organization.name}
        )
        {:ok, organization}
      error -> error
    end
  end

  @doc """
  Updates an organization.
  """
  def update_organization(%Organization{} = organization, attrs, updated_by_id \\ nil) do
    result = organization
    |> Organization.changeset(attrs)
    |> Repo.update()

    case result do
      {:ok, updated_organization} ->
        ActivityLogger.log_system_activity(updated_by_id, "organization_update",
          resource_type: "organization",
          resource_id: organization.id,
          metadata: %{name: updated_organization.name, changes: Map.keys(attrs)}
        )
        {:ok, updated_organization}
      error -> error
    end
  end

  @doc """
  Deletes an organization.
  """
  def delete_organization(%Organization{} = organization, deleted_by_id \\ nil) do
    result = Repo.delete(organization)

    case result do
      {:ok, deleted_organization} ->
        ActivityLogger.log_system_activity(deleted_by_id, "organization_delete",
          resource_type: "organization",
          resource_id: organization.id,
          metadata: %{name: organization.name}
        )
        {:ok, deleted_organization}
      error -> error
    end
  end

  ## Team Management

  @doc """
  Returns the list of teams for an organization.
  """
  def list_teams(organization_id, opts \\ []) do
    results = Team
    |> Team.by_organization(organization_id)
    |> maybe_search_teams(opts[:search])
    |> Team.with_member_count()
    |> Repo.all()

    # Convert tuple results to teams with member_count
    Enum.map(results, fn {team, member_count} ->
      Map.put(team, :member_count, member_count)
    end)
  end

  @doc """
  Creates a team.
  """
  def create_team(attrs \\ %{}, created_by_id \\ nil) do
    result = %Team{}
    |> Team.changeset(attrs)
    |> Repo.insert()

    case result do
      {:ok, team} ->
        ActivityLogger.log_system_activity(created_by_id, "team_create",
          resource_type: "team",
          resource_id: team.id,
          metadata: %{name: team.name, organization_id: team.organization_id}
        )
        {:ok, team}
      error -> error
    end
  end

  @doc """
  Gets a single team.
  """
  def get_team!(id), do: Repo.get!(Team, id)

  @doc """
  Gets a single team with users preloaded.
  """
  def get_team_with_users!(id) do
    Team
    |> Team.with_users()
    |> Repo.get!(id)
  end

  @doc """
  Updates a team.
  """
  def update_team(%Team{} = team, attrs, updated_by_id \\ nil) do
    result = team
    |> Team.changeset(attrs)
    |> Repo.update()

    case result do
      {:ok, updated_team} ->
        ActivityLogger.log_system_activity(updated_by_id, "team_update",
          resource_type: "team",
          resource_id: team.id,
          metadata: %{name: updated_team.name, changes: Map.keys(attrs)}
        )
        {:ok, updated_team}
      error -> error
    end
  end

  @doc """
  Deletes a team.
  """
  def delete_team(%Team{} = team, deleted_by_id \\ nil) do
    result = Repo.delete(team)

    case result do
      {:ok, deleted_team} ->
        ActivityLogger.log_system_activity(deleted_by_id, "team_delete",
          resource_type: "team",
          resource_id: team.id,
          metadata: %{name: team.name, organization_id: team.organization_id}
        )
        {:ok, deleted_team}
      error -> error
    end
  end

  @doc """
  Adds a user to a team with specified role.
  """
  def add_user_to_team(user_id, team_id, role \\ "member", added_by_id \\ nil) do
    attrs = %{
      user_id: user_id,
      team_id: team_id,
      role: role
    }

    result = %UserTeamMembership{}
    |> UserTeamMembership.changeset(attrs)
    |> Repo.insert()

    case result do
      {:ok, membership} ->
        ActivityLogger.log_system_activity(added_by_id, "team_member_add",
          resource_type: "user_team_membership",
          resource_id: membership.id,
          metadata: %{user_id: user_id, team_id: team_id, role: role}
        )
        {:ok, membership}
      error -> error
    end
  end

  @doc """
  Removes a user from a team.
  """
  def remove_user_from_team(user_id, team_id, removed_by_id \\ nil) do
    membership = UserTeamMembership
    |> UserTeamMembership.by_user(user_id)
    |> UserTeamMembership.by_team(team_id)
    |> Repo.one()

    if membership do
      result = Repo.delete(membership)

      case result do
        {:ok, deleted_membership} ->
          ActivityLogger.log_system_activity(removed_by_id, "team_member_remove",
            resource_type: "user_team_membership",
            resource_id: membership.id,
            metadata: %{user_id: user_id, team_id: team_id}
          )
          {:ok, deleted_membership}
        error -> error
      end
    else
      {:error, :not_found}
    end
  end

  @doc """
  Updates a user's role in a team.
  """
  def update_team_member_role(user_id, team_id, new_role, updated_by_id \\ nil) do
    membership = UserTeamMembership
    |> UserTeamMembership.by_user(user_id)
    |> UserTeamMembership.by_team(team_id)
    |> Repo.one()

    if membership do
      old_role = membership.role
      result = membership
      |> UserTeamMembership.changeset(%{role: new_role})
      |> Repo.update()

      case result do
        {:ok, updated_membership} ->
          ActivityLogger.log_system_activity(updated_by_id, "team_member_role_update",
            resource_type: "user_team_membership",
            resource_id: membership.id,
            metadata: %{user_id: user_id, team_id: team_id, old_role: old_role, new_role: new_role}
          )
          {:ok, updated_membership}
        error -> error
      end
    else
      {:error, :not_found}
    end
  end

  ## Permission Checking

  @doc """
  Check if user has permission for resource and action.
  """
  def user_has_permission?(user, resource, action) do
    User.has_permission?(user, resource, action)
  end

  @doc """
  Check if user has role.
  """
  def user_has_role?(user, role_name) do
    User.has_role?(user, role_name)
  end

  # Private functions

  defp create_user_record(attrs) do
    %User{}
    |> User.registration_changeset(attrs)
    |> Repo.insert()
  end

  defp create_user_record_with_password(attrs) do
    {changeset, generated_password} = %User{}
    |> User.admin_registration_changeset(attrs)

    case Repo.insert(changeset) do
      {:ok, user} ->
        {:ok, user, generated_password}
      {:error, changeset} ->
        {:error, changeset}
    end
  end

  defp create_user_profile(user, attrs) do
    # Convert all keys to strings and add user_id
    profile_attrs =
      attrs
      |> Enum.into(%{}, fn {k, v} -> {to_string(k), v} end)
      |> Map.put("user_id", user.id)

    %UserProfile{}
    |> UserProfile.changeset(profile_attrs)
    |> Repo.insert()
  end

  defp assign_default_role(user, assigned_by_id) do
    # Assign default "analyst" role to new users
    case get_role_by_name("analyst") do
      %Role{} = role ->
        assign_role_to_user(user.id, role.id, assigned_by_id)
      nil ->
        {:ok, nil}  # No default role found, continue without error
    end
  end

  defp maybe_filter_by_organization(query, nil), do: query
  defp maybe_filter_by_organization(query, organization_id) do
    User.by_organization(query, organization_id)
  end

  defp maybe_filter_by_status(query, nil), do: query
  defp maybe_filter_by_status(query, status) do
    User.by_status(query, status)
  end

  defp maybe_search_users(query, nil), do: query
  defp maybe_search_users(query, search_term) do
    User.search(query, search_term)
  end

  defp maybe_search_teams(query, nil), do: query
  defp maybe_search_teams(query, search_term) do
    Team.search(query, search_term)
  end

  @doc """
  Sends a welcome email to a new user with their temporary password.
  Returns {:ok, email_info} on success or {:error, reason} on failure.
  """
  defp send_welcome_email(user, temporary_password, login_url) do
    require Logger

    Logger.info("Sending welcome email to #{user.email} with temporary password")

    try do
      # Create and send the welcome email
      user
      |> Reconciliation.Emails.welcome_email(temporary_password, login_url)
      |> Reconciliation.Emails.deliver()
    rescue
      e ->
        Logger.error("Failed to send welcome email: #{inspect(e)}")
        {:error, "Email sending failed: #{inspect(e)}"}
    end
  end
end
