defmodule Reconciliation.EmailsTest do
  use ExUnit.Case
  import Swoosh.TestAssertions

  alias Reconciliation.Emails
  alias Reconciliation.Accounts.User

  describe "password_reset_email/2" do
    test "creates a proper password reset email" do
      user = %User{email: "<EMAIL>"}
      reset_url = "https://example.com/reset-password/token123"

      email = Emails.password_reset_email(user, reset_url)

      assert email.to == "<EMAIL>"
      assert email.from == {"<EMAIL>", "ProBASE Reconciliation"}
      assert email.subject =~ "Reset your password"
      assert email.html_body =~ reset_url
      assert email.text_body =~ reset_url
    end
  end

  describe "deliver/1" do
    test "delivers an email" do
      user = %User{email: "<EMAIL>"}
      reset_url = "https://example.com/reset-password/token123"

      email = Emails.password_reset_email(user, reset_url)
      Emails.deliver(email)

      assert_email_sent(subject: "Reset your password - ProBASE Reconciliation")
    end
  end
end
