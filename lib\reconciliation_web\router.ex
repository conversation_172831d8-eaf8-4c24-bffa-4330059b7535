defmodule ReconciliationWeb.Router do
  use ReconciliationWeb, :router

  import ReconciliationWeb.UserAuth

  pipeline :browser do
    plug :accepts, ["html"]
    plug :fetch_session
    plug :fetch_live_flash
    plug :put_root_layout, html: {ReconciliationWeb.Layouts, :root}
    plug :protect_from_forgery
    plug :put_secure_browser_headers
    plug :fetch_current_user
  end

  pipeline :api do
    plug :accepts, ["json"]
    plug :fetch_session
    plug :fetch_current_user
  end

  scope "/", ReconciliationWeb do
    pipe_through :browser

    # Redirect root to login page
    get "/", PageController, :redirect_to_login

    # Keep the original landing page available at /home for reference
    get "/home", PageController, :home
  end

  # API routes for external integrations
  scope "/api", ReconciliationWeb.Api, as: :api do
    pipe_through [:api, :require_authenticated_user]

    resources "/reconciliation", ReconciliationApiController, only: [:index, :show] do
      get "/transactions", ReconciliationApiController, :transactions
      get "/summary", ReconciliationApiController, :summary
    end
  end

  # Enable LiveDashboard in development
  if Application.compile_env(:reconciliation, :dev_routes) do
    # If you want to use the LiveDashboard in production, you should put
    # it behind authentication and allow only admins to access it.
    # If your application does not have an admins-only section yet,
    # you can use Plug.BasicAuth to set up some basic authentication
    # as long as you are also using SSL (which you should anyway).
    import Phoenix.LiveDashboard.Router

    scope "/dev" do
      pipe_through :browser

      live_dashboard "/dashboard", metrics: ReconciliationWeb.Telemetry
    end
  end

  ## Authentication routes

  scope "/", ReconciliationWeb do
    pipe_through [:browser, :redirect_if_user_is_authenticated]

    live_session :redirect_if_user_is_authenticated,
      on_mount: [{ReconciliationWeb.UserAuth, :redirect_if_user_is_authenticated}] do
      live "/users/register", UserRegistrationLive, :new
      live "/users/log_in", UserLoginLive, :new
      live "/users/forgot_password", UserForgotPasswordLive, :new
      live "/users/reset_password", UserForgotPasswordLive, :new
      live "/users/reset_password/:token", UserResetPasswordLive, :edit
    end

    post "/users/log_in", UserSessionController, :create
  end

  scope "/", ReconciliationWeb do
    pipe_through [:browser, :require_authenticated_user]

    live_session :require_authenticated_user,
      on_mount: [{ReconciliationWeb.UserAuth, :ensure_authenticated}] do
      live "/users/settings", UserSettingsLive, :edit
      live "/users/settings/confirm_email/:token", UserSettingsLive, :confirm_email
      live "/dashboard", DashboardLive, :index
      live "/summary", SummaryLive, :index
      live "/reconciliation", ReconciliationUploadLive, :index
      live "/reconciliation/:id/results", ReconciliationResultsLive, :show
      live "/reconciliation/:run_id/proof_reading", TransactionDebugLive, :index
      live "/reconciliation/:run_id/inspect", DataInspectionLive, :index
      live "/transactions", TransactionsLive, :index
      live "/reports", ReportsLive, :index
      live "/settings", SettingsLive, :index
      live "/activity", ActivityLogsLive, :index
    end

    # Reconciliation operation routes (non-LiveView)
    post "/reconciliation/:run_id/run_matching", ReconciliationController, :run_matching
    get "/reconciliation/:run_id/export/:format", ReconciliationController, :export_transactions
    post "/reconciliation/files/:file_id/process", ReconciliationController, :process_file
    get "/reconciliation/:run_id/status", ReconciliationController, :run_status
    get "/reconciliation/:run_id/summary", ReconciliationController, :transaction_summary



    # Admin routes for user management
    live_session :admin_user_management,
      on_mount: [{ReconciliationWeb.UserAuth, :ensure_authenticated}] do
      live "/admin/users", Admin.UsersLive, :index
      live "/admin/users/new", Admin.UsersLive, :new
      live "/admin/users/:id", Admin.UsersLive, :show
      live "/admin/users/:id/edit", Admin.UsersLive, :edit
      live "/admin/roles", Admin.RolesLive, :index
      live "/admin/roles/new", Admin.RolesLive, :new
      live "/admin/roles/:id", Admin.RolesLive, :show
      live "/admin/roles/:id/edit", Admin.RolesLive, :edit
      live "/admin/organizations", Admin.OrganizationsLive, :index
      live "/admin/organizations/new", Admin.OrganizationsLive, :new
      live "/admin/organizations/:id", Admin.OrganizationsLive, :show
      live "/admin/organizations/:id/edit", Admin.OrganizationsLive, :edit
      live "/admin/teams", Admin.TeamsLive, :index
      live "/admin/teams/new", Admin.TeamsLive, :new
      live "/admin/teams/:id", Admin.TeamsLive, :show
      live "/admin/teams/:id/edit", Admin.TeamsLive, :edit
      live "/admin/activity-logs", Admin.ActivityLogsLive, :index
    end
  end

  scope "/", ReconciliationWeb do
    pipe_through [:browser]

    delete "/users/log_out", UserSessionController, :delete

    live_session :current_user,
      on_mount: [{ReconciliationWeb.UserAuth, :mount_current_user}] do
      live "/users/confirm/:token", UserConfirmationLive, :edit
      live "/users/confirm", UserConfirmationInstructionsLive, :new
    end
  end
end
